from odoo import models, fields, api


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    # Custom field that was added via UI - keeping for compatibility
    x_amount = fields.Monetary(
        string='Custom Amount',
        currency_field='currency_id',
        help='Custom amount field added via UI'
    )

    def _get_sorted_order_lines(self):
        """Return order lines sorted by product type"""
        return self.order_line.sorted(key=lambda line: (
            line.product_id.get_product_sort_key() if line.product_id else 4,
            line.product_id.name if line.product_id else ''
        ))

    def _get_grouped_order_lines(self):
        """Return order lines grouped by product type for reports"""
        lines = self._get_sorted_order_lines()
        grouped = {
            'cigars': lines.filtered(lambda l: l.product_id and l.product_id.is_cigar),
            'cigarettes': lines.filtered(lambda l: l.product_id and l.product_id.is_cigarette),
            'tobacco': lines.filtered(lambda l: l.product_id and l.product_id.is_tobacco),
            'others': lines.filtered(lambda l: not l.product_id or (not l.product_id.is_cigar and not l.product_id.is_cigarette and not l.product_id.is_tobacco))
        }
        return grouped

    def _get_order_lines_by_type(self):
        """Return order lines organized by type for printing"""
        grouped = self._get_grouped_order_lines()
        result = []

        if grouped['cigars']:
            cigar_subtotal = sum(grouped['cigars'].mapped('price_subtotal'))
            result.append({
                'type': 'section',
                'name': 'CIGARS',
                'lines': grouped['cigars'],
                'subtotal': cigar_subtotal,
                'count': len(grouped['cigars'])
            })

        # Cigarettes temporarily commented out
        # if grouped['cigarettes']:
        #     cigarette_subtotal = sum(grouped['cigarettes'].mapped('price_subtotal'))
        #     result.append({
        #         'type': 'section',
        #         'name': 'CIGARETTES',
        #         'lines': grouped['cigarettes'],
        #         'subtotal': cigarette_subtotal,
        #         'count': len(grouped['cigarettes'])
        #     })

        if grouped['tobacco']:
            tobacco_subtotal = sum(grouped['tobacco'].mapped('price_subtotal'))
            result.append({
                'type': 'section',
                'name': 'TOBACCO PRODUCTS',
                'lines': grouped['tobacco'],
                'subtotal': tobacco_subtotal,
                'count': len(grouped['tobacco'])
            })

        if grouped['others']:
            others_subtotal = sum(grouped['others'].mapped('price_subtotal'))
            result.append({
                'type': 'section',
                'name': 'OTHER PRODUCTS',
                'lines': grouped['others'],
                'subtotal': others_subtotal,
                'count': len(grouped['others'])
            })

        return result

    def _get_section_totals(self):
        """Get totals for each product type section"""
        sections = self._get_order_lines_by_type()
        totals = {}
        for section in sections:
            totals[section['name']] = {
                'subtotal': section['subtotal'],
                'count': section['count'],
                'lines': section['lines']
            }
        return totals


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'
    _order = 'product_type_sort, sequence, id'

    product_type_sort = fields.Integer(
        string='Product Type Sort',
        compute='_compute_product_type_sort',
        store=True,
        help='Sort key for product type ordering'
    )

    @api.depends('product_id', 'product_id.is_cigar', 'product_id.is_cigarette', 'product_id.is_tobacco')
    def _compute_product_type_sort(self):
        """Compute sort key based on product type"""
        for line in self:
            if line.product_id:
                line.product_type_sort = line.product_id.get_product_sort_key()
            else:
                line.product_type_sort = 4

    @api.model
    def create(self, vals):
        """Override create to ensure proper sorting"""
        line = super(SaleOrderLine, self).create(vals)
        return line

    def write(self, vals):
        """Override write to ensure proper sorting when product changes"""
        result = super(SaleOrderLine, self).write(vals)
        if 'product_id' in vals:
            self._compute_product_type_sort()
            # Trigger reordering of lines
            self._reorder_lines()
        return result

    def _reorder_lines(self):
        """Reorder lines based on product type"""
        if self.order_id:
            lines = self.order_id.order_line.sorted(key=lambda l: (
                l.product_type_sort,
                l.product_id.name if l.product_id else '',
                l.id
            ))
            sequence = 10
            for line in lines:
                line.sequence = sequence
                sequence += 10
