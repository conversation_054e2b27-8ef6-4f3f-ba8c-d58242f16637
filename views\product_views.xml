<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Product Template Form View -->
        <record id="product_template_form_view_cigarette_tobacco" model="ir.ui.view">
            <field name="name">product.template.form.cigarette.tobacco</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='general_information']" position="after">
                    <page string="Product Classification" name="product_classification">
                        <group>
                            <group string="Product Type" col="2">
                                <field name="is_cigar"/>
                                <field name="is_cigarette"/>
                                <field name="is_tobacco"/>
                            </group>
                            <group string="Information" col="1">
                                <p class="text-muted">
                                    <strong>American Standards:</strong><br/>
                                    • Cigar products will use "Box(es)" as default unit (25 cigars per box)<br/>
                                    • Cigarette products will use "Pack(s)" as default unit (20 cigarettes per pack)<br/>
                                    • Tobacco products will use "fl oz" as default unit (American fluid ounces)<br/>
                                    • Only one type can be selected at a time
                                </p>
                            </group>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>

        <!-- Product Product Form View - Removed to avoid duplication in variants -->

        <!-- Tree views removed to avoid inheritance conflicts -->

        <!-- Add search filters -->
        <record id="product_template_search_view_cigarette_tobacco" model="ir.ui.view">
            <field name="name">product.template.search.cigarette.tobacco</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_search_view"/>
            <field name="arch" type="xml">
                <xpath expr="//search" position="inside">
                    <separator/>
                    <filter string="Cigars" name="cigars" domain="[('is_cigar', '=', True)]"/>
                    <filter string="Cigarettes" name="cigarettes" domain="[('is_cigarette', '=', True)]"/>
                    <filter string="Tobacco" name="tobacco" domain="[('is_tobacco', '=', True)]"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
