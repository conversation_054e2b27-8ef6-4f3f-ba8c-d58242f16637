<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Product Template Form View -->
        <record id="product_template_form_view_cigarette_tobacco" model="ir.ui.view">
            <field name="name">product.template.form.cigarette.tobacco</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='general_information']" position="after">
                    <page string="Product Classification" name="product_classification">
                        <group>
                            <group string="Product Type" col="2">
                                <field name="is_cigar"/>
                                <field name="is_cigarette"/>
                                <field name="is_tobacco"/>
                            </group>
                            <group string="Information" col="1">
                                <p class="text-muted">
                                    • Cigar products will use "Case(s)" as default unit of measure<br/>
                                    • Cigarette products will use "Stick(s)" as default unit of measure<br/>
                                    • Tobacco products will use "ml" as default unit of measure<br/>
                                    • Only one type can be selected at a time
                                </p>
                            </group>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>

        <!-- Product Product Form View - Removed to avoid duplication in variants -->

        <!-- Tree views removed to avoid inheritance conflicts -->

        <!-- Add search filters -->
        <record id="product_template_search_view_cigarette_tobacco" model="ir.ui.view">
            <field name="name">product.template.search.cigarette.tobacco</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_search_view"/>
            <field name="arch" type="xml">
                <xpath expr="//search" position="inside">
                    <separator/>
                    <filter string="Cigars" name="cigars" domain="[('is_cigar', '=', True)]"/>
                    <filter string="Cigarettes" name="cigarettes" domain="[('is_cigarette', '=', True)]"/>
                    <filter string="Tobacco" name="tobacco" domain="[('is_tobacco', '=', True)]"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
