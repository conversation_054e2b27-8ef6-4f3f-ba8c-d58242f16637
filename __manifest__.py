{
    'name': 'Product Cigarette & Tobacco Classification',
    'version': '********.0',
    'category': 'Sales',
    'summary': 'Add cigar, cigarette and tobacco classification to products with custom sorting',
    'description': """
Product Cigarette & Tobacco Classification
==========================================

This module adds:
* Boolean fields to classify products as cigars, cigarettes or tobacco
* Custom units of measure (cases/boxes for cigars, sticks for cigarettes, ml for tobacco)
* Sorted product lines in sales orders (cigars first, then cigarettes, then tobacco, then others)
* Enhanced product form view with classification fields

Features:
---------
* Cigar products are measured in cases/boxes (American standard)
* Cigarette products are measured in sticks
* Tobacco products are measured in ml
* Sales orders show products sorted by type automatically
* Easy product classification in product form
* Model-level sorting without view dependencies
    """,
    'author': 'Arihantai',
    'website': 'https://www.arihantai.com/',
    'depends': [
        'base',
        'product',
        'sale',
        'account',
        'uom',
    ],
    'data': [
        'security/ir.model.access.csv',
        'data/uom_data.xml',
        'views/product_views.xml',
    ],
    # 'demo': [
    #     'demo/product_demo.xml',
    # ],
    'installable': True,
    'auto_install': False,
    'application': False,
}
