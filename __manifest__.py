{
    'name': 'Product Cigarette & Tobacco Classification',
    'version': '********.0',
    'category': 'Sales',
    'summary': 'Add cigar, cigarette and tobacco classification to products with custom sorting',
    'description': """
Product Cigarette & Tobacco Classification
==========================================

This module adds:
* Boolean fields to classify products as cigars, cigarettes or tobacco
* Custom units of measure (cases/boxes for cigars, sticks for cigarettes, ml for tobacco)
* Sorted product lines in sales orders (cigars first, then cigarettes, then tobacco, then others)
* Enhanced product form view with classification fields

Features:
---------
* Cigar products are measured in cases/boxes (American standard)
* Cigarette products are measured in sticks
* Tobacco products are measured in ml
* Invoice PDF reports show products grouped by type (cigars first, then tobacco, then others)
* Easy product classification in product form
* No changes to sales order views - only affects invoice PDF reports
    """,
    'author': 'Arihantai',
    'website': 'https://www.arihantai.com/',
    'depends': [
        'base',
        'product',
        'sale',
        'account',
        'uom',
    ],
    'data': [
        'data/uom_data.xml',
        'views/product_views.xml',
        'reports/invoice_report.xml',
    ],
    
    'installable': True,
    'auto_install': False,
    'application': False,
}
