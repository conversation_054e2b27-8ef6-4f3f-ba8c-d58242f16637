<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Cigarette Products -->
        <record id="product_cigarette_marlboro" model="product.template">
            <field name="name">Demo Marlboro Red</field>
            <field name="type">product</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="is_cigarette" eval="True"/>
            <field name="is_tobacco" eval="False"/>
            <field name="uom_id" ref="product_cigarette_tobacco.uom_stick"/>
            <field name="uom_po_id" ref="product_cigarette_tobacco.uom_stick"/>
            <field name="list_price">0.50</field>
            <field name="standard_price">0.30</field>
            <field name="sale_ok" eval="True"/>
            <field name="purchase_ok" eval="True"/>
        </record>

        <record id="product_cigarette_camel" model="product.template">
            <field name="name">Demo Camel Blue</field>
            <field name="type">product</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="is_cigarette" eval="True"/>
            <field name="is_tobacco" eval="False"/>
            <field name="uom_id" ref="product_cigarette_tobacco.uom_stick"/>
            <field name="uom_po_id" ref="product_cigarette_tobacco.uom_stick"/>
            <field name="list_price">0.45</field>
            <field name="standard_price">0.28</field>
            <field name="sale_ok" eval="True"/>
            <field name="purchase_ok" eval="True"/>
        </record>

        <record id="product_cigarette_lucky" model="product.template">
            <field name="name">Demo Lucky Strike</field>
            <field name="type">product</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="is_cigarette" eval="True"/>
            <field name="is_tobacco" eval="False"/>
            <field name="uom_id" ref="product_cigarette_tobacco.uom_stick"/>
            <field name="uom_po_id" ref="product_cigarette_tobacco.uom_stick"/>
            <field name="list_price">0.48</field>
            <field name="standard_price">0.29</field>
            <field name="sale_ok" eval="True"/>
            <field name="purchase_ok" eval="True"/>
        </record>

        <!-- Demo Tobacco Products -->
        <record id="product_tobacco_virginia" model="product.template">
            <field name="name">Demo Virginia Tobacco Blend</field>
            <field name="type">product</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="is_cigarette" eval="False"/>
            <field name="is_tobacco" eval="True"/>
            <field name="uom_id" ref="product_cigarette_tobacco.uom_ml"/>
            <field name="uom_po_id" ref="product_cigarette_tobacco.uom_ml"/>
            <field name="list_price">0.05</field>
            <field name="standard_price">0.03</field>
            <field name="sale_ok" eval="True"/>
            <field name="purchase_ok" eval="True"/>
        </record>

        <record id="product_tobacco_burley" model="product.template">
            <field name="name">Demo Burley Tobacco</field>
            <field name="type">product</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="is_cigarette" eval="False"/>
            <field name="is_tobacco" eval="True"/>
            <field name="uom_id" ref="product_cigarette_tobacco.uom_ml"/>
            <field name="uom_po_id" ref="product_cigarette_tobacco.uom_ml"/>
            <field name="list_price">0.04</field>
            <field name="standard_price">0.025</field>
            <field name="sale_ok" eval="True"/>
            <field name="purchase_ok" eval="True"/>
        </record>

        <!-- Demo Other Products -->
        <record id="product_lighter" model="product.template">
            <field name="name">Demo Disposable Lighter</field>
            <field name="type">product</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="is_cigarette" eval="False"/>
            <field name="is_tobacco" eval="False"/>
            <field name="list_price">2.50</field>
            <field name="standard_price">1.20</field>
            <field name="sale_ok" eval="True"/>
            <field name="purchase_ok" eval="True"/>
        </record>

        <record id="product_ashtray" model="product.template">
            <field name="name">Demo Glass Ashtray</field>
            <field name="type">product</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="is_cigarette" eval="False"/>
            <field name="is_tobacco" eval="False"/>
            <field name="list_price">15.00</field>
            <field name="standard_price">8.00</field>
            <field name="sale_ok" eval="True"/>
            <field name="purchase_ok" eval="True"/>
        </record>

        <record id="product_rolling_papers" model="product.template">
            <field name="name">Demo Rolling Papers</field>
            <field name="type">product</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="is_cigarette" eval="False"/>
            <field name="is_tobacco" eval="False"/>
            <field name="list_price">1.25</field>
            <field name="standard_price">0.60</field>
            <field name="sale_ok" eval="True"/>
            <field name="purchase_ok" eval="True"/>
        </record>
    </data>
</odoo>
